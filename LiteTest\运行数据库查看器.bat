@echo off
chcp 65001 >nul
echo 🗄️  LiteDB 数据库查看器
echo ========================
echo.

REM 检查是否安装了 .NET
where dotnet >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到 .NET SDK
    echo 请从以下地址下载并安装 .NET 8.0 SDK:
    echo https://dotnet.microsoft.com/download
    echo.
    echo 或者使用 Python 版本: python SimpleDbReader.py
    pause
    exit /b 1
)

echo ✅ 找到 .NET SDK
echo 🔧 正在编译数据库查看器...
echo.

REM 编译数据库查看器
dotnet build DatabaseViewer.csproj -c Release -o bin

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败
    echo 尝试使用 Python 版本...
    echo.
    python SimpleDbReader.py
    pause
    exit /b 1
)

echo ✅ 编译成功
echo 🚀 正在启动数据库查看器...
echo.

REM 运行数据库查看器
bin\DatabaseViewer.exe "productdata-log_2025年08月13日15时51分58秒.db"

echo.
echo 程序已结束
pause
